import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { products as initialProducts } from '../data/products';
import DarkModeToggle from '../components/DarkModeToggle';

const AdminDashboard = () => {
  const [products, setProducts] = useState(initialProducts);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newProduct, setNewProduct] = useState({
    name: '',
    price: '',
    salePrice: '',
    category: '',
    description: '',
    sizes: ['S', 'M', 'L', 'XL', 'XXL'],
    image: '',
    stock: { S: 0, M: 0, L: 0, XL: 0, XXL: 0 }
  });

  const updateStock = (productId, size, change) => {
    setProducts(prev => prev.map(product => {
      if (product.id === productId) {
        const newStock = Math.max(0, product.stock[size] + change);
        return {
          ...product,
          stock: { ...product.stock, [size]: newStock }
        };
      }
      return product;
    }));
  };

  const handleAddProduct = (e) => {
    e.preventDefault();
    const product = {
      ...newProduct,
      id: Date.now(),
      price: parseInt(newProduct.price),
      salePrice: parseInt(newProduct.salePrice),
      discount: Math.round(((newProduct.price - newProduct.salePrice) / newProduct.price) * 100),
      image: newProduct.image || 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center'
    };
    
    setProducts(prev => [...prev, product]);
    setNewProduct({
      name: '',
      price: '',
      salePrice: '',
      category: '',
      description: '',
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      image: '',
      stock: { S: 0, M: 0, L: 0, XL: 0, XXL: 0 }
    });
    setShowAddForm(false);
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // In a real app, you'd upload to a server
      // For demo, we'll use a placeholder
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewProduct(prev => ({ ...prev, image: e.target.result }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark-bg transition-colors duration-300">
      <DarkModeToggle />
      
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          className="flex justify-between items-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div>
            <h1 className="text-4xl font-black gradient-text">Admin Dashboard</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">Manage your TrueGenZ inventory</p>
          </div>
          
          <motion.button
            onClick={() => setShowAddForm(true)}
            className="btn-primary"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Add New Product
          </motion.button>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {[
            { label: 'Total Products', value: products.length, color: 'from-blue-500 to-purple-600' },
            { label: 'Total Stock', value: products.reduce((sum, p) => sum + Object.values(p.stock).reduce((s, v) => s + v, 0), 0), color: 'from-green-500 to-teal-600' },
            { label: 'Low Stock Items', value: products.filter(p => Object.values(p.stock).some(s => s <= 5)).length, color: 'from-yellow-500 to-orange-600' },
            { label: 'Out of Stock', value: products.filter(p => Object.values(p.stock).every(s => s === 0)).length, color: 'from-red-500 to-pink-600' }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              className={`glass-card rounded-xl p-6 bg-gradient-to-br ${stat.color}`}
              whileHover={{ scale: 1.02, y: -2 }}
              transition={{ duration: 0.2 }}
            >
              <h3 className="text-white/80 text-sm font-medium">{stat.label}</h3>
              <p className="text-white text-3xl font-bold mt-2">{stat.value}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Products Table */}
        <motion.div
          className="glass-card rounded-xl overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="sticky top-0 bg-gray-50 dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">Product Inventory</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Stock Management</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                <AnimatePresence>
                  {products.map((product, index) => (
                    <motion.tr
                      key={product.id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <img className="h-12 w-12 rounded-lg object-cover" src={product.image} alt={product.name} />
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{product.name}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{product.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-primary-pink/20 text-primary-pink">
                          {product.category}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          <span className="font-bold">₹{product.salePrice}</span>
                          {product.price !== product.salePrice && (
                            <span className="ml-2 text-gray-500 line-through">₹{product.price}</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-2">
                          {product.sizes.map(size => (
                            <div key={size} className="flex items-center space-x-2">
                              <span className="text-sm font-medium w-8 text-gray-700 dark:text-gray-300">{size}:</span>
                              <motion.button
                                onClick={() => updateStock(product.id, size, -1)}
                                className="w-8 h-8 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                disabled={product.stock[size] === 0}
                              >
                                -
                              </motion.button>
                              <span className={`w-12 text-center text-sm font-bold ${
                                product.stock[size] === 0 ? 'text-red-500' : 
                                product.stock[size] <= 5 ? 'text-yellow-500' : 
                                'text-green-500'
                              }`}>
                                {product.stock[size]}
                              </span>
                              <motion.button
                                onClick={() => updateStock(product.id, size, 1)}
                                className="w-8 h-8 rounded-full bg-green-500 text-white flex items-center justify-center hover:bg-green-600 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                +
                              </motion.button>
                            </div>
                          ))}
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </tbody>
            </table>
          </div>
        </motion.div>
      </div>

      {/* Add Product Modal */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowAddForm(false)}
          >
            <motion.div
              className="glass-card rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="text-2xl font-bold gradient-text mb-6">Add New Product</h2>
              
              <form onSubmit={handleAddProduct} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Product Name"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    required
                  />
                  <input
                    type="text"
                    placeholder="Category"
                    value={newProduct.category}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    required
                  />
                  <input
                    type="number"
                    placeholder="Original Price"
                    value={newProduct.price}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, price: e.target.value }))}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    required
                  />
                  <input
                    type="number"
                    placeholder="Sale Price"
                    value={newProduct.salePrice}
                    onChange={(e) => setNewProduct(prev => ({ ...prev, salePrice: e.target.value }))}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                    required
                  />
                </div>
                
                <textarea
                  placeholder="Product Description"
                  value={newProduct.description}
                  onChange={(e) => setNewProduct(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white h-24"
                  required
                />
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Product Image
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Note: In production, images would be uploaded to a server. For demo purposes, a placeholder will be used.
                  </p>
                </div>

                <div className="grid grid-cols-5 gap-2">
                  {['S', 'M', 'L', 'XL', 'XXL'].map(size => (
                    <div key={size}>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {size} Stock
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={newProduct.stock[size]}
                        onChange={(e) => setNewProduct(prev => ({
                          ...prev,
                          stock: { ...prev.stock, [size]: parseInt(e.target.value) || 0 }
                        }))}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                      />
                    </div>
                  ))}
                </div>

                <div className="flex space-x-4 pt-4">
                  <motion.button
                    type="submit"
                    className="flex-1 btn-primary"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Add Product
                  </motion.button>
                  <motion.button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Cancel
                  </motion.button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdminDashboard;
