@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-white dark:bg-dark-bg text-gray-900 dark:text-white font-poppins;
    margin: 0;
    min-height: 100vh;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10;
  }

  .neon-glow {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(255, 65, 129, 0.3), 0 0 40px rgba(142, 36, 170, 0.2);
  }

  .neon-glow:hover {
    box-shadow: 0 0 30px rgba(255, 65, 129, 0.5), 0 0 60px rgba(142, 36, 170, 0.3);
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-pink to-primary-purple bg-clip-text text-transparent;
  }

  .btn-primary {
    @apply bg-brand-gradient text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-primary:hover {
    @apply neon-glow;
  }

  .card-hover {
    @apply transition-all duration-300 transform hover:scale-105 hover:-translate-y-2;
  }

  .scroll-reveal {
    @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
  }

  .scroll-reveal.revealed {
    @apply opacity-100 translate-y-0;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 3D Transform utilities */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Brand gradient utility */
  .bg-brand-gradient {
    background: linear-gradient(135deg, #ff4181, #8e24aa);
  }

  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Animation utilities */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
}
