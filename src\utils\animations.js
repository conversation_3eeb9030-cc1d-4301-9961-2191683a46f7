// Framer Motion animation variants
export const fadeInUp = {
  hidden: { 
    opacity: 0, 
    y: 60 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const fadeInLeft = {
  hidden: { 
    opacity: 0, 
    x: -60 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const fadeInRight = {
  hidden: { 
    opacity: 0, 
    x: 60 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const scaleIn = {
  hidden: { 
    opacity: 0, 
    scale: 0.8 
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

export const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

export const slideInFromBottom = {
  hidden: { 
    opacity: 0, 
    y: 100 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
};

export const rotateIn = {
  hidden: { 
    opacity: 0, 
    rotate: -180,
    scale: 0.5
  },
  visible: { 
    opacity: 1, 
    rotate: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
};

export const bounceIn = {
  hidden: { 
    opacity: 0, 
    scale: 0.3 
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
      type: "spring",
      bounce: 0.4
    }
  }
};

// GSAP animation helpers
export const gsapFadeInUp = (element, delay = 0) => {
  return gsap.fromTo(element, 
    { 
      opacity: 0, 
      y: 50 
    },
    { 
      opacity: 1, 
      y: 0, 
      duration: 0.8, 
      delay,
      ease: "power2.out" 
    }
  );
};

export const gsapStaggerFadeIn = (elements, stagger = 0.1) => {
  return gsap.fromTo(elements,
    {
      opacity: 0,
      y: 30
    },
    {
      opacity: 1,
      y: 0,
      duration: 0.6,
      stagger,
      ease: "power2.out"
    }
  );
};

export const gsapScaleIn = (element, delay = 0) => {
  return gsap.fromTo(element,
    {
      opacity: 0,
      scale: 0.8
    },
    {
      opacity: 1,
      scale: 1,
      duration: 0.5,
      delay,
      ease: "back.out(1.7)"
    }
  );
};

export const gsapSlideInFromSide = (element, direction = 'left', delay = 0) => {
  const xValue = direction === 'left' ? -100 : 100;
  
  return gsap.fromTo(element,
    {
      opacity: 0,
      x: xValue
    },
    {
      opacity: 1,
      x: 0,
      duration: 0.8,
      delay,
      ease: "power2.out"
    }
  );
};

// Hover animation presets
export const hoverScale = {
  whileHover: { 
    scale: 1.05,
    transition: { duration: 0.2 }
  },
  whileTap: { 
    scale: 0.95 
  }
};

export const hoverGlow = {
  whileHover: { 
    boxShadow: "0 0 25px rgba(255, 65, 129, 0.4)",
    transition: { duration: 0.3 }
  }
};

export const hoverFloat = {
  whileHover: { 
    y: -8,
    transition: { duration: 0.3 }
  }
};

export const hoverRotate = {
  whileHover: { 
    rotate: 5,
    transition: { duration: 0.3 }
  }
};

// Page transition variants
export const pageTransition = {
  initial: { opacity: 0, x: 300 },
  in: { opacity: 1, x: 0 },
  out: { opacity: 0, x: -300 }
};

export const pageVariants = {
  initial: {
    opacity: 0,
    y: 20
  },
  in: {
    opacity: 1,
    y: 0
  },
  out: {
    opacity: 0,
    y: -20
  }
};

export const pageTransitions = {
  type: "tween",
  ease: "anticipate",
  duration: 0.5
};
