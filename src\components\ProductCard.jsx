import { motion } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import { useScrollReveal } from '../hooks/useScrollReveal';
import { gsap } from 'gsap';
import { hoverScale, hoverFloat } from '../utils/animations';

const ProductCard = ({ product, onAddToCart }) => {
  const [selectedSize, setSelectedSize] = useState(product.sizes[0]);
  const [isFlipped, setIsFlipped] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useScrollReveal();
  const imageRef = useRef(null);
  const priceRef = useRef(null);

  useEffect(() => {
    // GSAP hover animations
    const image = imageRef.current;
    const price = priceRef.current;

    if (isHovered) {
      gsap.to(image, { scale: 1.1, duration: 0.3, ease: "power2.out" });
      gsap.to(price, { scale: 1.05, duration: 0.2, ease: "power2.out" });
    } else {
      gsap.to(image, { scale: 1, duration: 0.3, ease: "power2.out" });
      gsap.to(price, { scale: 1, duration: 0.2, ease: "power2.out" });
    }
  }, [isHovered]);

  const handleBuyNow = () => {
    onAddToCart?.(product, selectedSize);
    // Add some visual feedback
    setIsFlipped(true);
    setTimeout(() => setIsFlipped(false), 600);
  };

  return (
    <motion.div
      ref={cardRef}
      className="scroll-reveal group"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      {...hoverFloat}
      transition={{ duration: 0.3 }}
    >
      <div className="relative h-96 perspective-1000">
        <motion.div
          className="relative w-full h-full transition-transform duration-600 transform-style-preserve-3d"
          animate={{ rotateY: isFlipped ? 180 : 0 }}
        >
          {/* Front of card */}
          <div className="absolute inset-0 w-full h-full backface-hidden">
            <div className="glass-card rounded-xl overflow-hidden h-full group-hover:neon-glow transition-all duration-300">
              {/* Product Image */}
              <div className="relative overflow-hidden h-48">
                <motion.img
                  ref={imageRef}
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  loading="lazy"
                  initial={{ scale: 1 }}
                />
                
                {/* Discount Badge */}
                {product.discount > 0 && (
                  <motion.div
                    className="absolute top-3 right-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-bold"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    -{product.discount}%
                  </motion.div>
                )}

                {/* Category Badge */}
                <div className="absolute top-3 left-3 bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs">
                  {product.category}
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4 space-y-3">
                <h3 className="font-bold text-lg text-gray-900 dark:text-white group-hover:gradient-text transition-all duration-300">
                  {product.name}
                </h3>
                
                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                  {product.description}
                </p>

                {/* Price */}
                <motion.div
                  ref={priceRef}
                  className="flex items-center space-x-2"
                >
                  <motion.span
                    className="text-2xl font-bold gradient-text"
                    animate={isHovered ? { scale: 1.1 } : { scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    ₹{product.salePrice}
                  </motion.span>
                  {product.price !== product.salePrice && (
                    <span className="text-lg text-gray-500 line-through">
                      ₹{product.price}
                    </span>
                  )}
                </motion.div>

                {/* Size Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Size:
                  </label>
                  <select
                    value={selectedSize}
                    onChange={(e) => setSelectedSize(e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-pink focus:border-transparent transition-all duration-200"
                  >
                    {product.sizes.map((size) => (
                      <option key={size} value={size}>
                        {size} {product.stock[size] <= 5 && `(Only ${product.stock[size]} left)`}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Buy Button */}
                <motion.button
                  onClick={handleBuyNow}
                  className="w-full btn-primary"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  disabled={product.stock[selectedSize] === 0}
                >
                  {product.stock[selectedSize] === 0 ? 'Out of Stock' : 'Buy Now'}
                </motion.button>
              </div>
            </div>
          </div>

          {/* Back of card */}
          <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180">
            <div className="glass-card rounded-xl h-full flex items-center justify-center bg-gradient-to-br from-green-400 to-blue-500">
              <div className="text-center text-white p-6">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: isFlipped ? 1 : 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <svg className="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <h3 className="text-xl font-bold mb-2">Added to Cart!</h3>
                  <p className="text-sm opacity-90">
                    {product.name} - Size {selectedSize}
                  </p>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ProductCard;
